<template>
  <header class="header">
    <div class="header-left">
      <div class="logo">
        <img :src="createDefaultLogo()" class="logo" alt="" />
        <h3 class="name">影像大脑</h3>
      </div>
      <ul class="navigation">
        <li
          class="navigation-item"
          :class="[{ active: currentKey == item.key }]"
          v-for="item in menuList"
          :key="item.key"
          @click="onJumpRoute(item.path)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="header-right">
      <section id="commonHead" style="height: 60px"></section>
    </div>
  </header>
</template>

<script setup lang="ts">
import { common } from "@ys/tools";
import { menuPermission } from "@/api";
import { useRoute, useRouter } from "vue-router";
import { ref, onMounted, watch } from "vue";
import { getUrlQuery } from "@ys/tools/common/path";

const { createDefaultLogo, on2NoPermission } = common;

const router = useRouter();
const route = useRoute();
const currentKey = ref("");
const list = ref([
  {
    path: "/ai-course/index",
    key: "ai-course",
    name: "AI课程分析",
    auth: 1,
  },
  {
    path: "/ai-class-async/index",
    key: "ai-class-async",
    name: "同课异构分析",
    auth: 2,
  },
  {
    path: "/data-aggregation/layout",
    key: "data-aggregation",
    name: "数据汇总分析",
    auth: 3,
  },
  {
    path: "/analyse-task/layout",
    key: "analyse-task",
    name: "分析任务管理",
    auth: 4,
  },
  {
    path: "/setting/layout",
    key: "setting",
    name: "设置",
    auth: 0,
  },
]);

const menuList = ref<any[]>([]);

const onJumpRoute = (path: string) => {
  router.push(path);
};

watch(
  () => route,
  (newV) => {
    if (newV.meta && newV.meta.key) {
      currentKey.value = newV.meta.key as string;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const handleOnJumpMenu = (menuList: any[]) => {
  const first = menuList[0];
  if (first) {
    if (route.path == "/layout") {
      const bureauId = getUrlQuery("bureauId");
      const jd = getUrlQuery("jd");
      router.replace({
        path: first.path,
        query: {
          bureauId: bureauId,
          jd: jd,
        },
      });
    }
  } else {
    on2NoPermission();
  }
};

const getMenuPermission = async () => {
  const response = await menuPermission({ menu: "ai" });
  const res = response.data as any;
  if (res.code == 0) {
    const data = res.data;
    let menuIds = data.menuIds as number[];
    // 判断是否有0的权限
    if (!menuIds.includes(0)) {
      on2NoPermission();
      return;
    }
    menuList.value = list.value.filter((item) => {
      if (menuIds.includes(item.auth)) {
        return item;
      }
    });
    sessionStorage.setItem("menuIds", JSON.stringify(menuIds));
    sessionStorage.setItem("menuList", JSON.stringify(menuList.value));
    handleOnJumpMenu(menuList.value);
  }
};

onMounted(() => {
  (window as any).yskjConfig.createHeadOperation("commonHead", {
    isPureBackground: true,
  });
  const _menuList = sessionStorage.getItem("menuList");
  if (_menuList) {
    menuList.value = JSON.parse(_menuList);
    handleOnJumpMenu(menuList.value);
  } else {
    getMenuPermission();
  }
});
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  z-index: 999;
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  padding: 0 12px 0 24px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0 4px 8px 0 rgba(0, 5, 32, 0.02);
  .header-left {
    display: flex;
    align-items: center;
    height: 60px;
    .logo {
      display: flex;
      align-items: center;
      height: 60px;
      img {
        width: 32px;
        height: 32px;
        /* font-size: 24px; */
      }
      h3 {
        margin: 0;
        margin-left: 8px;
        font-weight: bold;
        font-size: 20px;
        color: #262626;
      }
    }
    .navigation {
      list-style: none;
      padding: 0;
      margin: 0;
      margin-left: 124px;
      display: flex;
      align-items: center;
      .navigation-item {
        margin-right: 56px;
        font-size: 16px;
        line-height: 24px;
        color: #262626;
        font-weight: 400;
        cursor: pointer;
        &.active {
          font-weight: bold;
          color: #007aff;
        }
      }
    }
  }
}
</style>
